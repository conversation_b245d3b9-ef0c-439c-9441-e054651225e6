# -*- coding: utf-8 -*-
# 运行此脚本需要安装以下依赖：
# 1. Python 库: moviepy (pip install moviepy)
# 2. 系统工具: ffmpeg (可以通过 Chocolatey 安装: choco install ffmpeg)

import os
import argparse
import sys
from moviepy.editor import VideoFileClip, concatenate_videoclips

def add_intro_outro_no_convert(target_dir):
    """遍历目录，为视频文件添加片头和片尾，不进行格式转换。"""
    # 获取脚本所在的目录
    script_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    intro_outro_file = os.path.join(script_dir, 'powerfull.mp4') # 片头片尾文件在脚本同目录下
    output_subdir = os.path.join(target_dir, 'processed_videos')

    if not os.path.isfile(intro_outro_file):
        print(f"错误：在脚本目录 '{script_dir}' 中未找到 'powerfull.mp4' 文件。")
        return

    if not os.path.exists(output_subdir):
        os.makedirs(output_subdir)
        print(f"创建输出子目录：'{output_subdir}'")

    try:
        intro_clip = VideoFileClip(intro_outro_file)
        outro_clip = VideoFileClip(intro_outro_file) # 可以复用同一个对象，但为了清晰分开写
    except Exception as e:
        print(f"错误：无法加载片头/片尾视频 '{intro_outro_file}': {e}")
        return

    video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.wmv') # 可根据需要添加更多视频格式
    found_videos = False

    print(f"开始处理目录 '{target_dir}' 中的视频文件（不进行转换）...")

    for filename in os.listdir(target_dir):
        if filename.lower().endswith(video_extensions) and filename != 'powerfull.mp4':
            found_videos = True
            input_path = os.path.join(target_dir, filename)
            output_filename = f"{os.path.splitext(filename)[0]}_processed.mp4"
            output_path = os.path.join(output_subdir, output_filename)

            print(f"  处理文件：'{filename}'")
            main_clip = None # 初始化 main_clip
            final_clip = None # 初始化 final_clip

            try:
                # 1. 直接加载原始主视频
                print(f"    加载原始视频：'{filename}'")
                main_clip = VideoFileClip(input_path)

                # 2. 拼接视频 (使用原始 intro/outro 和原始 main_clip)
                # 注意：如果视频尺寸/帧率不匹配，拼接可能会失败或效果不佳。
                # MoviePy 的 concatenate_videoclips 默认会尝试处理，但结果可能不如预期。
                # method="compose" 可能有助于处理不同尺寸，但仍需注意兼容性。
                print(f"    开始拼接视频...")
                final_clip = concatenate_videoclips([intro_clip, main_clip, outro_clip], method="compose")

                # 3. 写入最终文件 (使用与原始视频相似的编码，或默认设置)
                # 移除特定的 codec='libx264', audio_codec='aac'，让 moviepy 自动处理或使用默认值
                # 如果需要特定编码，可以重新添加，但需确保兼容性
                final_clip.write_videofile(output_path)

                print(f"    成功处理并保存到：'{output_path}'")

            except Exception as e:
                print(f"    处理文件 '{filename}' 时出错: {e}")
            finally:
                # 4. 释放资源
                if main_clip:
                    main_clip.close()
                if final_clip:
                    final_clip.close()

    # 处理完所有文件后关闭片头片尾剪辑
    intro_clip.close()
    outro_clip.close()

    if not found_videos:
        print(f"在目录 '{target_dir}' 中未找到其他视频文件（不包括 'powerfull.mp4'）。")
    else:
        print("处理完成。")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='为指定目录下的所有视频文件添加片头和片尾（不进行格式转换）。')
    parser.add_argument('directory', type=str, help='包含视频文件的目录路径')

    args = parser.parse_args()

    if not os.path.isdir(args.directory):
        print(f"错误：提供的路径 '{args.directory}' 不是一个有效的目录。")
    else:
        add_intro_outro_no_convert(args.directory)
    # 为了方便测试，可以取消注释下面这行并替换为你本地的测试目录
    # add_intro_outro_no_convert('C:\\path\\to\\your\\videos')