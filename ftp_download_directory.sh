#!/bin/bash

# FTP目录下载脚本
# 用法: ./ftp_download_directory.sh <ftp_server> <username> <password> <remote_dir> <local_dir>

# 检查参数数量
if [ $# -ne 5 ]; then
    echo "用法: $0 <ftp_server> <username> <password> <remote_dir> <local_dir>"
    echo "示例: $0 ftp.example.com myuser mypass /remote/path /local/path"
    exit 1
fi

# 参数赋值
FTP_SERVER="$1"
USERNAME="$2"
PASSWORD="$3"
REMOTE_DIR="$4"
LOCAL_DIR="$5"

# 日志文件
LOG_FILE="ftp_download_$(date +%Y%m%d_%H%M%S).log"

# 记录日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 测试FTP连接
test_ftp_connection() {
    log_message "测试FTP连接..."

    local test_result=$(lftp -c "
        set net:timeout 10;
        set net:max-retries 1;
        set ftp:passive-mode on;
        open ftp://$USERNAME:$PASSWORD@$FTP_SERVER;
        pwd;
        quit
    " 2>&1)

    if echo "$test_result" | grep -q "^/"; then
        local current_dir=$(echo "$test_result" | grep "^/" | head -1)
        log_message "FTP连接成功，当前目录: $current_dir"
        return 0
    else
        log_message "FTP连接失败:"
        echo "$test_result" | tee -a "$LOG_FILE"
        return 1
    fi
}

# 创建本地目录
create_local_dir() {
    local dir="$1"
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir" 2>/dev/null
        if [ $? -eq 0 ]; then
            log_message "创建目录: $dir"
        else
            log_message "错误: 无法创建目录 $dir"
            return 1
        fi
    fi
    return 0
}

# 下载文件函数
download_file() {
    local remote_file="$1"
    local local_file="$2"
    
    # 创建本地文件的目录
    local local_dir=$(dirname "$local_file")
    create_local_dir "$local_dir"
    
    # 使用wget下载文件
    wget --ftp-user="$USERNAME" --ftp-password="$PASSWORD" \
         --no-verbose --timeout=30 --tries=3 \
         "ftp://$FTP_SERVER$remote_file" -O "$local_file" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_message "下载成功: $remote_file -> $local_file"
        return 0
    else
        log_message "下载失败: $remote_file (跳过)"
        rm -f "$local_file" 2>/dev/null
        return 1
    fi
}

# 递归下载目录函数
download_directory() {
    local remote_path="$1"
    local local_path="$2"
    
    log_message "处理目录: $remote_path"
    
    # 创建本地目录
    create_local_dir "$local_path"
    
    # 获取目录列表
    local temp_list="/tmp/ftp_list_$$.txt"
    
    # 使用lftp获取目录列表
    log_message "尝试连接FTP服务器并获取目录列表..."

    lftp -c "
        set ftp:list-options -a;
        set net:timeout 10;
        set net:max-retries 1;
        set ftp:passive-mode on;
        debug 3;
        open ftp://$USERNAME:$PASSWORD@$FTP_SERVER;
        cd '$remote_path' || exit 1;
        ls -la > $temp_list;
        quit
    " 2>&1 | tee -a "$LOG_FILE"
    
    if [ ! -f "$temp_list" ] || [ ! -s "$temp_list" ]; then
        log_message "错误: 无法获取目录列表 $remote_path (跳过)"
        rm -f "$temp_list" 2>/dev/null
        return 1
    fi
    
    # 解析目录列表
    while IFS= read -r line; do
        # 跳过空行和总计行
        if [[ -z "$line" || "$line" =~ ^total ]]; then
            continue
        fi
        
        # 解析文件信息
        if [[ "$line" =~ ^([drwx-]+)[[:space:]]+[0-9]+[[:space:]]+[^[:space:]]+[[:space:]]+[^[:space:]]+[[:space:]]+[0-9]+[[:space:]]+[A-Za-z]+[[:space:]]+[0-9]+[[:space:]]+[0-9:]+[[:space:]]+(.+)$ ]]; then
            local permissions="${BASH_REMATCH[1]}"
            local filename="${BASH_REMATCH[2]}"
            
            # 跳过当前目录和父目录
            if [[ "$filename" == "." || "$filename" == ".." ]]; then
                continue
            fi
            
            local remote_item="$remote_path/$filename"
            local local_item="$local_path/$filename"
            
            # 检查是否为目录
            if [[ "$permissions" =~ ^d ]]; then
                log_message "发现子目录: $filename"
                download_directory "$remote_item" "$local_item"
            else
                log_message "发现文件: $filename"
                download_file "$remote_item" "$local_item"
            fi
        fi
    done < "$temp_list"
    
    # 清理临时文件
    rm -f "$temp_list" 2>/dev/null
}

# 主函数
main() {
    log_message "开始FTP下载任务"
    log_message "FTP服务器: $FTP_SERVER"
    log_message "用户名: $USERNAME"
    log_message "远程目录: $REMOTE_DIR"
    log_message "本地目录: $LOCAL_DIR"
    
    # 检查必要的工具
    if ! command -v lftp &> /dev/null; then
        log_message "错误: 需要安装lftp工具"
        echo "请运行: sudo apt-get install lftp"
        exit 1
    fi

    if ! command -v wget &> /dev/null; then
        log_message "错误: 需要安装wget工具"
        echo "请运行: sudo apt-get install wget"
        exit 1
    fi

    # 测试FTP连接
    if ! test_ftp_connection; then
        log_message "错误: FTP连接失败，请检查服务器地址、用户名和密码"
        exit 1
    fi

    # 开始下载
    download_directory "$REMOTE_DIR" "$LOCAL_DIR"
    
    log_message "FTP下载任务完成"
    log_message "日志文件: $LOG_FILE"
}

# 执行主函数
main
