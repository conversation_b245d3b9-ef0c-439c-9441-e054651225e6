# 检查并自动提升管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    # 以管理员权限重启脚本
    Start-Process powershell.exe "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
    Exit
}

# 设置执行策略为Bypass
try {
    Set-ExecutionPolicy Bypass -Scope Process -Force
} catch {
    Write-Warning "无法设置执行策略: $_"
    Exit
}

Write-Host "开始彻底清除MySQL..." -ForegroundColor Cyan

# 1. 停止所有MySQL相关服务
Write-Host "正在停止MySQL服务..." -ForegroundColor Yellow
Get-Service | Where-Object {$_.Name -like "mysql*" -or $_.DisplayName -like "*mysql*"} | ForEach-Object {
    Write-Host "  停止服务: $($_.Name)" -ForegroundColor Gray
    Stop-Service -Name $_.Name -Force -ErrorAction SilentlyContinue
}

# 2. 使用Chocolatey卸载MySQL（如果是通过Chocolatey安装的）
Write-Host "正在使用Chocolatey卸载MySQL..." -ForegroundColor Yellow
choco uninstall mysql -y -ErrorAction SilentlyContinue

# 3. 使用Windows卸载程序移除MySQL组件
Write-Host "正在使用Windows卸载程序移除MySQL组件..." -ForegroundColor Yellow
$mysqlProducts = Get-WmiObject -Class Win32_Product | Where-Object {$_.Name -like "*MySQL*"}
foreach ($product in $mysqlProducts) {
    Write-Host "  卸载: $($product.Name)" -ForegroundColor Gray
    $product.Uninstall() | Out-Null
}

# 4. 删除MySQL服务
Write-Host "正在删除MySQL服务..." -ForegroundColor Yellow
$services = Get-WmiObject -Class Win32_Service | Where-Object {$_.Name -like "*mysql*" -or $_.DisplayName -like "*mysql*"}
foreach ($service in $services) {
    Write-Host "  删除服务: $($service.Name)" -ForegroundColor Gray
    $service.Delete() | Out-Null
}

# 5. 删除MySQL程序文件
Write-Host "正在删除MySQL程序文件..." -ForegroundColor Yellow
$foldersToRemove = @(
    "C:\Program Files\MySQL",
    "C:\Program Files (x86)\MySQL",
    "C:\ProgramData\MySQL",
    "C:\ProgramData\Package Cache\*mysql*",
    "C:\Users\<USER>\AppData\Local\MySQL",
    "C:\Users\<USER>\AppData\Roaming\MySQL"
)

foreach ($folder in $foldersToRemove) {
    Get-Item -Path $folder -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "  删除文件夹: $_" -ForegroundColor Gray
        Remove-Item -Path $_ -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# 6. 删除MySQL数据目录
Write-Host "正在删除MySQL数据目录..." -ForegroundColor Yellow
$dataFolders = @(
    "C:\ProgramData\MySQL",
    "C:\mysql\data"
)

foreach ($folder in $dataFolders) {
    if (Test-Path $folder) {
        Write-Host "  删除数据目录: $folder" -ForegroundColor Gray
        Remove-Item -Path $folder -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# 7. 删除MySQL注册表项
Write-Host "正在删除MySQL注册表项..." -ForegroundColor Yellow
$registryKeys = @(
    "HKLM:\SOFTWARE\MySQL",
    "HKLM:\SOFTWARE\Wow6432Node\MySQL",
    "HKLM:\SYSTEM\CurrentControlSet\Services\MySQL",
    "HKLM:\SYSTEM\CurrentControlSet\Services\EventLog\Application\MySQL",
    "HKCU:\Software\MySQL"
)

foreach ($key in $registryKeys) {
    if (Test-Path $key) {
        Write-Host "  删除注册表项: $key" -ForegroundColor Gray
        Remove-Item -Path $key -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# 8. 清理环境变量中的MySQL路径
Write-Host "正在清理环境变量..." -ForegroundColor Yellow
$pathsToRemove = @("*mysql*")

# 清理系统PATH环境变量
$systemPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
$newSystemPath = ($systemPath -split ';' | Where-Object { 
    $path = $_
    $remove = $false
    foreach ($pattern in $pathsToRemove) {
        if ($path -like $pattern) {
            $remove = $true
            break
        }
    }
    -not $remove
}) -join ';'

if ($systemPath -ne $newSystemPath) {
    Write-Host "  更新系统PATH环境变量" -ForegroundColor Gray
    [Environment]::SetEnvironmentVariable("Path", $newSystemPath, "Machine")
}

# 清理用户PATH环境变量
$userPath = [Environment]::GetEnvironmentVariable("Path", "User")
if ($userPath) {
    $newUserPath = ($userPath -split ';' | Where-Object { 
        $path = $_
        $remove = $false
        foreach ($pattern in $pathsToRemove) {
            if ($path -like $pattern) {
                $remove = $true
                break
            }
        }
        -not $remove
    }) -join ';'

    if ($userPath -ne $newUserPath) {
        Write-Host "  更新用户PATH环境变量" -ForegroundColor Gray
        [Environment]::SetEnvironmentVariable("Path", $newUserPath, "User")
    }
}

# 9. 删除MySQL相关的临时文件
Write-Host "正在删除临时文件..." -ForegroundColor Yellow
Remove-Item -Path "$env:TEMP\MySQL*" -Recurse -Force -ErrorAction SilentlyContinue

# 10. 检查是否还有MySQL进程在运行
$mysqlProcesses = Get-Process | Where-Object {$_.Name -like "*mysql*"} -ErrorAction SilentlyContinue
if ($mysqlProcesses) {
    Write-Host "发现正在运行的MySQL进程，正在终止..." -ForegroundColor Yellow
    $mysqlProcesses | ForEach-Object {
        Write-Host "  终止进程: $($_.Name) (PID: $($_.Id))" -ForegroundColor Gray
        Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
    }
}

# 11. 验证MySQL是否已完全卸载
Write-Host "`n正在验证MySQL是否已完全卸载..." -ForegroundColor Cyan

$mysqlExists = $false

# 检查服务
$mysqlService = Get-Service | Where-Object {$_.Name -like "*mysql*" -or $_.DisplayName -like "*mysql*"} -ErrorAction SilentlyContinue
if ($mysqlService) {
    Write-Host "  仍然存在MySQL服务" -ForegroundColor Red
    $mysqlExists = $true
}

# 检查程序文件
foreach ($folder in $foldersToRemove) {
    if (Test-Path $folder) {
        Write-Host "  仍然存在MySQL文件夹: $folder" -ForegroundColor Red
        $mysqlExists = $true
    }
}

# 检查注册表
foreach ($key in $registryKeys) {
    if (Test-Path $key) {
        Write-Host "  仍然存在MySQL注册表项: $key" -ForegroundColor Red
        $mysqlExists = $true
    }
}

if ($mysqlExists) {
    Write-Host "`nMySQL未能完全卸载，可能需要重启计算机后再次运行此脚本。" -ForegroundColor Yellow
} else {
    Write-Host "`nMySQL已成功完全卸载！" -ForegroundColor Green
}

Write-Host "`n建议重启计算机以确保所有更改生效。" -ForegroundColor Cyan
$restart = Read-Host "是否立即重启计算机？(Y/N)"
if ($restart -eq "Y" -or $restart -eq "y") {
    Restart-Computer -Force
}