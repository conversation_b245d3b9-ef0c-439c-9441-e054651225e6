# 检查脚本语法
$scriptPath = "c:\Users\<USER>\source\windows\clean_mysql.ps1"

# 读取脚本内容
$content = Get-Content -Path $scriptPath -Raw

# 计算左右花括号数量
$leftBraces = ($content.ToCharArray() | Where-Object { $_ -eq '{' } | Measure-Object).Count
$rightBraces = ($content.ToCharArray() | Where-Object { $_ -eq '}' } | Measure-Object).Count

Write-Host "左花括号数量: $leftBraces"
Write-Host "右花括号数量: $rightBraces"

if ($leftBraces -eq $rightBraces) {
    Write-Host '花括号数量匹配，语法可能正确' -ForegroundColor Green
} else {
    Write-Host '花括号数量不匹配，语法可能有误' -ForegroundColor Red
    Write-Host "左花括号比右花括号多: $($leftBraces - $rightBraces)" -ForegroundColor Red
}

# 尝试使用PowerShell解析器检查语法
try {
    $null = [System.Management.Automation.Language.Parser]::ParseFile($scriptPath, [ref]$null, [ref]$null)
    Write-Host '脚本语法检查通过' -ForegroundColor Green
} catch {
    Write-Host '脚本存在语法错误：' -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}