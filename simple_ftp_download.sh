#!/bin/bash

# 简单的FTP目录下载脚本（使用原生ftp命令）
# 用法: ./simple_ftp_download.sh <ftp_server> <username> <password> <remote_dir> <local_dir>

# 检查参数
if [ $# -ne 5 ]; then
    echo "用法: $0 <ftp_server> <username> <password> <remote_dir> <local_dir>"
    echo "示例: $0 ftp.example.com myuser mypass /remote/path /local/path"
    exit 1
fi

FTP_SERVER="$1"
USERNAME="$2"
PASSWORD="$3"
REMOTE_DIR="$4"
LOCAL_DIR="$5"

# 创建本地目录
mkdir -p "$LOCAL_DIR"

# 生成FTP脚本
cat > /tmp/ftp_script_$$.txt << EOF
open $FTP_SERVER
user $USERNAME $PASSWORD
binary
prompt off
cd $REMOTE_DIR
lcd $LOCAL_DIR
mget *
quit
EOF

echo "开始下载 $REMOTE_DIR 到 $LOCAL_DIR"
echo "FTP服务器: $FTP_SERVER"

# 执行FTP命令
ftp -n < /tmp/ftp_script_$$.txt

# 清理临时文件
rm -f /tmp/ftp_script_$$.txt

echo "下载完成！"
