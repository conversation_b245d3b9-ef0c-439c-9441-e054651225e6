# Check and automatically elevate administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    # Restart script with administrator privileges
    Start-Process powershell.exe "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
    Exit
}

# Set execution policy to Bypass
try {
    Set-ExecutionPolicy Bypass -Scope Process -Force
} catch {
    Write-Warning "Unable to set execution policy: $_"
    Exit
}

# Create temporary directory for downloads
$tempDir = "$env:TEMP\dev_tools_temp"
New-Item -ItemType Directory -Force -Path $tempDir | Out-Null

# Install Chocolatey
Write-Host "Installing Chocolatey package manager..."
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
Iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))

# Refresh environment variables
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

# Install Python 3.10
Write-Host "Installing Python 3.10..."
choco install python3 --version=3.10.11 -y

# Install latest Go
Write-Host "Installing Go..."
choco install golang -y

# Install Git
Write-Host "Installing Git..."
choco install git -y

# Install SVN
Write-Host "Installing SVN..."
choco install svn -y

# Install Maven
Write-Host "Installing Maven..."
choco install maven -y

# Install VS Code
Write-Host "Installing VS Code..."
choco install vscode -y

# Install Node.js
Write-Host "Installing Node.js..."
choco install nodejs-lts -y

# Install Docker Desktop
Write-Host "Installing Docker Desktop..."
choco install docker-desktop -y

# Install Postman
Write-Host "Installing Postman..."
choco install postman -y

# Install MySQL
Write-Host "Installing MySQL..."
choco install mysql -y

# Install Redis
Write-Host "Installing Redis..."
choco install redis-64 -y

# Install WinSCP
Write-Host "Installing WinSCP..."
choco install winscp -y

# Install Chrome
Write-Host "Installing Chrome..."
choco install googlechrome -y

# Install Firefox
Write-Host "Installing Firefox..."
choco install firefox -y

# Install 7-Zip
Write-Host "Installing 7-Zip..."
choco install 7zip -y

# Install Notepad++
Write-Host "Installing Notepad++..."
choco install notepadplusplus -y

# Download and install JDK 1.8.411
Write-Host "Downloading and installing JDK 1.8.411..."
$jdkUrl = "https://javadl.oracle.com/webapps/download/GetFile/1.8.0_411-b06/jdk-8u411-windows-x64.exe"
$jdkInstaller = "$tempDir\jdk-8u411-windows-x64.exe"

try {
    # Download JDK installer
    (New-Object System.Net.WebClient).DownloadFile($jdkUrl, $jdkInstaller)
    
    # Silent install JDK
    Start-Process -FilePath $jdkInstaller -ArgumentList "/s" -Wait
    
    # Set JAVA_HOME environment variable
    $javaHome = "C:\Program Files\Java\jdk1.8.0_411"
    [System.Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHome, "Machine")
    
    # Add Java to PATH
    $javaPath = "$javaHome\bin"
    $currentPath = [System.Environment]::GetEnvironmentVariable("Path", "Machine")
    if ($currentPath -notlike "*$javaPath*") {
        [System.Environment]::SetEnvironmentVariable("Path", "$currentPath;$javaPath", "Machine")
    }
} catch {
    Write-Error "Error installing JDK: $_"
}

# Verify installations
Write-Host "`nVerifying installations..."

$commands = @(
    @{Name="Python"; Cmd="python --version"},
    @{Name="Go"; Cmd="go version"},
    @{Name="Git"; Cmd="git --version"},
    @{Name="SVN"; Cmd="svn --version"},
    @{Name="Maven"; Cmd="mvn --version"},
    @{Name="Java"; Cmd="java -version"},
    @{Name="VS Code"; Cmd="code --version"},
    @{Name="Node.js"; Cmd="node --version"},
    @{Name="Docker"; Cmd="docker --version"},
    @{Name="MySQL"; Cmd="mysql --version"},
    @{Name="Redis"; Cmd="redis-cli --version"},
    @{Name="7-Zip"; Cmd="7z"},
    @{Name="Notepad++"; Cmd="notepad++.exe --help"}
)

foreach ($command in $commands) {
    Write-Host "`nChecking $($command.Name):"
    try {
        Invoke-Expression $command.Cmd
    } catch {
        Write-Warning "$($command.Name) may not be properly installed or configured"
    }
}

# Clean up temporary files
Remove-Item -Path $tempDir -Recurse -Force

Write-Host "`nInstallation complete! Please restart PowerShell for all changes to take effect."
