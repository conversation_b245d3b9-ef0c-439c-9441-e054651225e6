# -*- coding: utf-8 -*-
# 运行此脚本需要安装以下依赖：
# 1. Python 库: moviepy (pip install moviepy)
# 2. 系统工具: ffmpeg (可以通过 Chocolatey 安装: choco install ffmpeg)

import os
import argparse
import sys
from moviepy.editor import VideoFileClip, concatenate_videoclips

def add_intro_outro(target_dir):
    """遍历目录，为视频文件添加片头和片尾。"""
    # 获取脚本所在的目录
    script_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    intro_outro_file = os.path.join(script_dir, 'powerfull.mp4') # 片头片尾文件在脚本同目录下
    output_subdir = os.path.join(target_dir, 'processed_videos')

    if not os.path.isfile(intro_outro_file):
        print(f"错误：在脚本目录 '{script_dir}' 中未找到 'powerfull.mp4' 文件。")
        return

    if not os.path.exists(output_subdir):
        os.makedirs(output_subdir)
        print(f"创建输出子目录：'{output_subdir}'")

    try:
        intro_clip = VideoFileClip(intro_outro_file)
        outro_clip = VideoFileClip(intro_outro_file) # 可以复用同一个对象，但为了清晰分开写
    except Exception as e:
        print(f"错误：无法加载片头/片尾视频 '{intro_outro_file}': {e}")
        return

    video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.wmv') # 可根据需要添加更多视频格式
    found_videos = False

    print(f"开始处理目录 '{target_dir}' 中的视频文件...")

    for filename in os.listdir(target_dir):
        if filename.lower().endswith(video_extensions) and filename != 'powerfull.mp4':
            found_videos = True
            input_path = os.path.join(target_dir, filename)
            output_filename = f"{os.path.splitext(filename)[0]}_processed.mp4"
            output_path = os.path.join(output_subdir, output_filename)

            print(f"  处理文件：'{filename}'")
            temp_converted_path = None # 初始化临时文件路径变量

            try:
                # 1. 转换主视频到兼容格式 (H.264/AAC)
                print(f"    转换 '{filename}' 为兼容格式...")
                temp_converted_filename = f"{os.path.splitext(filename)[0]}_temp_converted.mp4"
                temp_converted_path = os.path.join(output_subdir, temp_converted_filename) # 临时文件放在输出目录

                # 使用原始视频加载并直接写入为兼容格式
                with VideoFileClip(input_path) as temp_clip:
                    temp_clip.write_videofile(temp_converted_path, codec='libx264', audio_codec='aac', logger=None) # logger=None 避免过多输出
                print(f"    转换完成，临时文件：'{temp_converted_path}'")

                # 2. 加载转换后的主视频
                main_clip = VideoFileClip(temp_converted_path)

                # 3. 拼接视频
                # 确保所有剪辑的尺寸和帧率一致，以第一个剪辑为准或手动设置
                # 这里简单处理，可能需要更复杂的逻辑来处理不同分辨率/帧率的视频
                # final_clip = concatenate_videoclips([intro_clip, main_clip, outro_clip])

                # 尝试调整分辨率和帧率以匹配主视频（如果需要）
                # intro_clip_resized = intro_clip.resize(height=main_clip.h).set_fps(main_clip.fps)
                # outro_clip_resized = outro_clip.resize(height=main_clip.h).set_fps(main_clip.fps)
                # final_clip = concatenate_videoclips([intro_clip_resized, main_clip, outro_clip_resized])

                # 使用原始 intro/outro，如果尺寸/帧率不匹配可能会有问题
                # 注意：现在 main_clip 是转换后的，理论上 intro/outro 也应转换或调整以匹配
                # 为简化，暂时仍用原始 intro/outro，但推荐对 intro/outro 也进行预处理
                print(f"    开始拼接视频...")
                final_clip = concatenate_videoclips([intro_clip, main_clip, outro_clip], method="compose")

                # 4. 写入最终文件
                final_clip.write_videofile(output_path, codec='libx264', audio_codec='aac')

                print(f"    成功处理并保存到：'{output_path}'")

                # 5. 释放资源
                main_clip.close()
                final_clip.close()

            except Exception as e:
                print(f"    处理文件 '{filename}' 时出错: {e}")
                # 尝试关闭可能已打开的剪辑
                if 'main_clip' in locals() and main_clip:
                    main_clip.close()
                if 'final_clip' in locals() and final_clip:
                    final_clip.close()
                # 清理临时转换文件（如果存在且转换成功）
                if temp_converted_path and os.path.exists(temp_converted_path):
                    try:
                        os.remove(temp_converted_path)
                        print(f"    已删除临时文件：'{temp_converted_path}'")
                    except OSError as rm_err:
                        print(f"    警告：无法删除临时文件 '{temp_converted_path}': {rm_err}")

    # 处理完所有文件后关闭片头片尾剪辑
    intro_clip.close()
    outro_clip.close()

    if not found_videos:
        print(f"在目录 '{target_dir}' 中未找到其他视频文件（不包括 'powerfull.mp4'）。")
    else:
        print("处理完成。")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='为指定目录下的所有视频文件添加片头和片尾。')
    parser.add_argument('directory', type=str, help='包含视频文件和 powerfull.mp4 的目录路径')

    args = parser.parse_args()

    # if not os.path.isdir(args.directory):
        # print(f"错误：提供的路径 '{args.directory}' 不是一个有效的目录。")
    # else:
        # add_intro_outro(args.directory)
    add_intro_outro('C:\\迅雷下载\\1024')