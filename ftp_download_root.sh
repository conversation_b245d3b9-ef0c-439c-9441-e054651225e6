#!/bin/bash

# FTP根目录下载脚本
# 用法: ./ftp_download_root.sh <ftp_server> <username> <password> <local_dir>

if [ $# -ne 4 ]; then
    echo "用法: $0 <ftp_server> <username> <password> <local_dir>"
    echo "示例: $0 ftp.litsignage.com <EMAIL> password /www/wwwroot/"
    exit 1
fi

FTP_SERVER="$1"
USERNAME="$2"
PASSWORD="$3"
LOCAL_DIR="$4"

LOG_FILE="ftp_download_$(date +%Y%m%d_%H%M%S).log"

log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 测试连接并获取根目录路径
test_and_get_root() {
    log_message "测试FTP连接并获取根目录..."
    
    lftp -c "
        set net:timeout 30;
        set net:max-retries 3;
        set ftp:passive-mode on;
        open ftp://$USERNAME:$PASSWORD@$FTP_SERVER;
        pwd;
        ls -la;
        quit
    " 2>&1 | tee -a "$LOG_FILE"
}

# 使用lftp镜像下载整个目录
mirror_download() {
    log_message "开始镜像下载FTP根目录..."
    
    mkdir -p "$LOCAL_DIR"
    
    lftp -c "
        set net:timeout 30;
        set net:max-retries 3;
        set ftp:passive-mode on;
        set mirror:use-pget-n 5;
        set mirror:parallel-transfer-count 3;
        open ftp://$USERNAME:$PASSWORD@$FTP_SERVER;
        mirror --verbose --continue --delete --parallel=3 / '$LOCAL_DIR';
        quit
    " 2>&1 | tee -a "$LOG_FILE"
}

# 主函数
main() {
    log_message "开始FTP根目录下载任务"
    log_message "FTP服务器: $FTP_SERVER"
    log_message "用户名: $USERNAME"
    log_message "本地目录: $LOCAL_DIR"
    
    # 检查lftp
    if ! command -v lftp &> /dev/null; then
        log_message "错误: 需要安装lftp工具"
        echo "请运行: sudo apt-get install lftp"
        exit 1
    fi
    
    # 测试连接
    test_and_get_root
    
    # 开始镜像下载
    mirror_download
    
    log_message "FTP下载任务完成"
    log_message "日志文件: $LOG_FILE"
}

main
