# 检查并自动提升管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "此脚本需要管理员权限才能安装软件。正在尝试以管理员身份重新启动..."
    Start-Process powershell.exe "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
    Exit
}

# 确保 Chocolatey 已安装
Write-Host "检查 Chocolatey 是否已安装..."
$chocoPath = Get-Command choco -ErrorAction SilentlyContinue
if (-not $chocoPath) {
    Write-Host "未找到 Chocolatey，正在尝试安装..."
    Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    # 刷新环境变量以识别 choco
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    $chocoPath = Get-Command choco -ErrorAction SilentlyContinue
    if (-not $chocoPath) {
        Write-Error "Chocolatey 安装失败。请手动安装 Chocolatey 后重试。"
        Exit 1
    }
    Write-Host "Chocolatey 安装成功。"
} else {
    Write-Host "Chocolatey 已安装。"
}

# 安装 Python (如果需要)
Write-Host "检查 Python 是否已安装..."
$pythonPath = Get-Command python -ErrorAction SilentlyContinue
if (-not $pythonPath) {
    Write-Host "未找到 Python，正在使用 Chocolatey 安装 Python 3..."
    choco install python -y --force
    # 刷新环境变量以识别 python 和 pip
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    $pythonPath = Get-Command python -ErrorAction SilentlyContinue
    if (-not $pythonPath) {
        Write-Error "Python 安装失败。请检查 Chocolatey 日志。"
        Exit 1
    }
    Write-Host "Python 安装成功。"
} else {
    Write-Host "Python 已安装。"
}

# 确保 pip 可用并安装 moviepy
Write-Host "检查 pip 是否可用并安装 moviepy..."
$pipPath = Get-Command pip -ErrorAction SilentlyContinue
if (-not $pipPath) {
    # 尝试通过 python -m ensurepip 安装
    Write-Host "未直接找到 pip 命令，尝试通过 Python 确保 pip 已安装..."
    python -m ensurepip --upgrade
    # 再次刷新环境变量
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    $pipPath = Get-Command pip -ErrorAction SilentlyContinue
    if (-not $pipPath) {
       Write-Error "无法找到或安装 pip。请确保 Python 安装正确并包含 pip。"
       Exit 1
    }
}

Write-Host "使用 pip 安装 moviepy..."
pip install moviepy
if ($LASTEXITCODE -ne 0) {
    Write-Error "moviepy 安装失败。请检查 pip 日志。"
    Exit 1
}
Write-Host "moviepy 安装成功。"

# 安装 ffmpeg
Write-Host "使用 Chocolatey 安装 ffmpeg..."
choco install ffmpeg -y --force
if ($LASTEXITCODE -ne 0) {
    Write-Error "ffmpeg 安装失败。请检查 Chocolatey 日志。"
    Exit 1
}
Write-Host "ffmpeg 安装成功。"

Write-Host "所有依赖项（Python, pip, moviepy, ffmpeg）已检查或安装完毕。"
Read-Host "按 Enter 键退出..."