@echo off
setlocal enabledelayedexpansion

REM Batch script to attempt a thorough removal of MySQL from Windows.
REM Run this script as Administrator.

REM Check for Administrator privileges
openfiles > nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This script requires Administrator privileges. Please run as Administrator.
    pause
    exit /b 1
)

echo MySQL Removal Script
echo ====================
echo.
echo WARNING: This script will attempt to stop MySQL services, uninstall MySQL,
echo delete related files/folders, and remove registry keys.
echo It is recommended to back up important data before proceeding.
echo.
pause

REM --- Step 1: Stop MySQL Services ---
echo [1/7] Stopping MySQL services...
for /f "tokens=1,*" %%a in ('sc query state^= all ^| findstr /i "mysql"') do (
    if /i "%%a"=="SERVICE_NAME:" (
        set "service_name=%%b"
        set "service_name=!service_name: =!"
        echo   Stopping service: !service_name!
        net stop "!service_name!" > nul 2>&1
        sc delete "!service_name!" > nul 2>&1
        if !errorlevel! equ 0 (
            echo     Service !service_name! stopped and deleted.
        ) else (
            echo     WARN: Could not stop or delete service !service_name!. It might not exist or require manual intervention.
        )
    )
)
echo   Service stop attempt finished.

REM --- Step 2: Uninstall MySQL via WMIC ---
echo.
echo [2/7] Attempting to uninstall MySQL products via WMIC...
for /f "tokens=*" %%p in ('wmic product where "Name like '%%MySQL%%'" get Name, IdentifyingNumber /value ^| find "="') do (
    set "line=%%p"
    if "!line:~0,4!"=="Name" (
        set "product_name=!line:~5!"
    )
    if "!line:~0,17!"=="IdentifyingNumber" (
        set "product_id=!line:~18!"
        echo   Uninstalling: !product_name! (!product_id!)
        wmic product where "IdentifyingNumber='!product_id!'" call uninstall /nointeractive > nul 2>&1
        if !errorlevel! equ 0 (
            echo     Uninstalled !product_name! successfully.
        ) else (
            echo     WARN: Failed to uninstall !product_name! via WMIC. Manual uninstallation might be required.
        )
    )
)
echo   WMIC uninstall attempt finished.

REM --- Step 3: Delete MySQL Files and Folders ---
echo.
echo [3/7] Deleting MySQL files and folders...
set "folders_to_delete="
set "folders_to_delete=!folders_to_delete! "%ProgramFiles%\MySQL""
set "folders_to_delete=!folders_to_delete! "%ProgramFiles(x86)%\MySQL""
set "folders_to_delete=!folders_to_delete! "%ProgramData%\MySQL""
set "folders_to_delete=!folders_to_delete! "%APPDATA%\MySQL""
set "folders_to_delete=!folders_to_delete! "%LOCALAPPDATA%\MySQL""
set "folders_to_delete=!folders_to_delete! "C:\mysql""

for %%f in (!folders_to_delete!) do (
    if exist %%f (
        echo   Deleting folder: %%~f
        rd /s /q %%f > nul 2>&1
        if exist %%f (
            echo     WARN: Could not delete folder %%~f. Check permissions or delete manually.
        ) else (
            echo     Deleted %%~f.
        )
    ) else (
        echo   Folder not found: %%~f
    )
)

REM Delete Package Cache entries (use with caution)
echo   Searching and deleting MySQL entries in ProgramData\Package Cache...
for /d /r "%ProgramData%\Package Cache" %%d in (*mysql*) do (
    if exist "%%d" (
        echo     Deleting cache entry: %%d
        rd /s /q "%%d" > nul 2>&1
    )
)
echo   File deletion attempt finished.

REM --- Step 4: Delete MySQL Registry Keys ---
echo.
echo [4/7] Deleting MySQL registry keys...
set "reg_keys="
set "reg_keys=!reg_keys! "HKLM\SOFTWARE\MySQL AB""
set "reg_keys=!reg_keys! "HKLM\SOFTWARE\MySQL""
set "reg_keys=!reg_keys! "HKLM\SOFTWARE\Wow6432Node\MySQL AB""
set "reg_keys=!reg_keys! "HKLM\SOFTWARE\Wow6432Node\MySQL""
set "reg_keys=!reg_keys! "HKCU\Software\MySQL AB""
set "reg_keys=!reg_keys! "HKCU\Software\MySQL""

for %%k in (!reg_keys!) do (
    echo   Attempting to delete registry key: %%~k
    reg delete %%~k /f > nul 2>&1
    if !errorlevel! equ 0 (
        echo     Deleted registry key %%~k.
    ) else (
        echo     WARN: Could not delete registry key %%~k or key not found.
    )
)
echo   Registry deletion attempt finished.

REM --- Step 5: Clean PATH Environment Variable ---
echo.
echo [5/7] Cleaning MySQL entries from PATH environment variable...
set "new_system_path="
set "new_user_path="
set "path_changed=0"

REM Process System PATH
for /f "tokens=2,*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v Path') do set "system_path=%%b"
call :CleanPath system_path new_system_path
if !path_changed! equ 1 (
    echo   Updating System PATH.
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v Path /t REG_EXPAND_SZ /d "!new_system_path!" /f > nul
)

REM Process User PATH
set "path_changed=0"
for /f "tokens=2,*" %%a in ('reg query "HKCU\Environment" /v Path 2^>nul') do set "user_path=%%b"
if defined user_path (
    call :CleanPath user_path new_user_path
    if !path_changed! equ 1 (
        echo   Updating User PATH.
        reg add "HKCU\Environment" /v Path /t REG_EXPAND_SZ /d "!new_user_path!" /f > nul
    )
) else (
    echo   User PATH variable not found or empty.
)

echo   PATH cleaning finished. Changes might require a system restart to take full effect.

REM --- Step 6: Kill Remaining MySQL Processes ---
echo.
echo [6/7] Killing remaining MySQL processes (mysqld.exe)...
taskkill /F /IM mysqld.exe /T > nul 2>&1
if !errorlevel! equ 0 (
    echo   mysqld.exe processes terminated.
) else (
    echo   No running mysqld.exe processes found or unable to terminate.
)

REM --- Step 7: Final Message ---
echo.
echo [7/7] MySQL removal process finished.
echo Please review the output above for any warnings or errors.
echo A system restart is recommended to ensure all changes are applied.
echo.
pause
endlocal
exit /b 0

REM --- Subroutine to clean PATH variable ---
:CleanPath
setlocal
set "input_path_var=%~1"
set "output_path_var=%~2"
set "original_path=!%input_path_var%!"
set "cleaned_path="

REM Replace semicolons with newlines to process each entry
set "temp_path=!original_path:;=
!"

for /f "delims=" %%p in ("!temp_path!") do (
    set "entry=%%p"
    echo !entry! | findstr /i /c:"mysql" > nul
    if !errorlevel! neq 0 (
        if defined cleaned_path (
            set "cleaned_path=!cleaned_path!;%%p"
        ) else (
            set "cleaned_path=%%p"
        )
    )
)

REM Check if path actually changed
if "!original_path!" neq "!cleaned_path!" (
    set "path_changed_local=1"
) else (
    set "path_changed_local=0"
)

endlocal & set "%output_path_var%=%cleaned_path%" & set "path_changed=%path_changed_local%"
goto :eof