# 检查并自动提升管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    # 以管理员权限重启脚本
    Start-Process powershell.exe "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
    Exit
}

# 设置执行策略为Bypass
try {
    Set-ExecutionPolicy Bypass -Scope Process -Force
} catch {
    Write-Warning "无法设置执行策略: $_"
    # 即使设置失败也尝试继续，某些环境可能有限制
}

Write-Host "开始彻底清除MySQL..." -ForegroundColor Cyan

# --- 清理步骤 --- 

# 1. 停止所有MySQL相关服务
Write-Host "`n[1/11] 正在停止MySQL服务..." -ForegroundColor Yellow
$mysqlServices = Get-Service | Where-Object {$_.Name -like "mysql*" -or $_.DisplayName -like "*mysql*"} -ErrorAction SilentlyContinue
if ($mysqlServices) {
    foreach ($service in $mysqlServices) {
        Write-Host "  停止服务: $($service.Name)" -ForegroundColor Gray
        Stop-Service -Name $service.Name -Force -ErrorAction SilentlyContinue
    }
} else {
    Write-Host "  未找到活动的MySQL服务." -ForegroundColor Green
}

# 2. 使用Chocolatey卸载MySQL（如果存在）
Write-Host "`n[2/11] 正在尝试使用Chocolatey卸载MySQL..." -ForegroundColor Yellow
if (Get-Command choco -ErrorAction SilentlyContinue) {
    choco uninstall mysql -y --force -ErrorAction SilentlyContinue
    Write-Host "  Chocolatey卸载命令已执行 (如果安装了MySQL)." -ForegroundColor Gray
} else {
    Write-Host "  未找到Chocolatey，跳过此步骤." -ForegroundColor Gray
}

# 3. 使用Windows卸载程序移除MySQL组件 (WMI)
Write-Host "`n[3/11] 正在使用WMI移除MySQL组件..." -ForegroundColor Yellow
$mysqlProducts = Get-WmiObject -Class Win32_Product -Filter "Name LIKE '%MySQL%'" -ErrorAction SilentlyContinue
if ($mysqlProducts) {
    foreach ($product in $mysqlProducts) {
        Write-Host "  卸载: $($product.Name)" -ForegroundColor Gray
        try {
            $product.Uninstall() | Out-Null
        } catch {
            Write-Warning "  卸载 $($product.Name) 时出错: $_"
        }
    }
} else {
    Write-Host "  未通过WMI找到MySQL产品." -ForegroundColor Green
}

# 4. 删除MySQL服务 (以防卸载未完全删除)
Write-Host "`n[4/11] 正在删除残留的MySQL服务..." -ForegroundColor Yellow
$mysqlServicesToDelete = Get-WmiObject -Class Win32_Service | Where-Object {$_.Name -like "mysql*" -or $_.DisplayName -like "*mysql*"} -ErrorAction SilentlyContinue
if ($mysqlServicesToDelete) {
    foreach ($service in $mysqlServicesToDelete) {
        Write-Host "  删除服务: $($service.Name)" -ForegroundColor Gray
        try {
            $service.Delete() | Out-Null
        } catch {
            Write-Warning "  删除服务 $($service.Name) 时出错: $_"
        }
    }
} else {
    Write-Host "  未找到残留的MySQL服务." -ForegroundColor Green
}

# 5. 删除MySQL程序文件
Write-Host "`n[5/11] 正在删除MySQL程序文件..." -ForegroundColor Yellow
$programFolders = @(
    Join-Path $env:ProgramFiles "MySQL",
    Join-Path ${env:ProgramFiles(x86)} "MySQL",
    Join-Path $env:ProgramData "MySQL",
    Join-Path $env:LOCALAPPDATA "MySQL",
    Join-Path $env:APPDATA "MySQL"
)
# 通配符路径需要单独处理
$packageCachePath = Join-Path $env:ProgramData "Package Cache\*mysql*"

foreach ($folder in $programFolders) {
    if (Test-Path $folder) {
        Write-Host "  删除文件夹: $folder" -ForegroundColor Gray
        Remove-Item -Path $folder -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# 处理Package Cache中的MySQL相关条目
Get-Item -Path $packageCachePath -ErrorAction SilentlyContinue | ForEach-Object {
    Write-Host "  删除Package Cache条目: $($_.FullName)" -ForegroundColor Gray
    Remove-Item -Path $_.FullName -Recurse -Force -ErrorAction SilentlyContinue
}

# 6. 删除MySQL数据目录 (常见位置)
Write-Host "`n[6/11] 正在删除MySQL数据目录..." -ForegroundColor Yellow
$dataFolders = @(
    "C:\mysql\data", # 常见自定义位置
    Join-Path $env:ProgramData "MySQL\MySQL Server*\Data" # 默认安装位置下的Data
)

foreach ($folder in $dataFolders) {
    if (Test-Path $folder) {
        Write-Host "  删除数据目录: $folder" -ForegroundColor Gray
        Remove-Item -Path $folder -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# 7. 删除MySQL注册表项
Write-Host "`n[7/11] 正在删除MySQL注册表项..." -ForegroundColor Yellow
$registryKeys = @(
    "HKLM:\SOFTWARE\MySQL AB",
    "HKLM:\SOFTWARE\MySQL",
    "HKLM:\SOFTWARE\Wow6432Node\MySQL AB",
    "HKLM:\SOFTWARE\Wow6432Node\MySQL",
    "HKCU:\Software\MySQL AB",
    "HKCU:\Software\MySQL"
    # 服务相关的键通常在步骤4中通过服务删除处理，但可以加一层保险
    # "HKLM:\SYSTEM\CurrentControlSet\Services\MySQL*" # 使用通配符可能危险，精确匹配更好
)

foreach ($key in $registryKeys) {
    if (Test-Path $key) {
        Write-Host "  删除注册表项: $key" -ForegroundColor Gray
        Remove-Item -Path $key -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# 8. 清理环境变量中的MySQL路径
Write-Host "`n[8/11] 正在清理环境变量中的MySQL路径..." -ForegroundColor Yellow
$envChanged = $false

# 清理系统PATH
$systemPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
$newSystemPathEntries = $systemPath -split ';' | Where-Object { $_ -notlike '*mysql*' }
$newSystemPath = $newSystemPathEntries -join ';'
if ($systemPath -ne $newSystemPath) {
    Write-Host "  更新系统PATH环境变量" -ForegroundColor Gray
    [Environment]::SetEnvironmentVariable("Path", $newSystemPath, "Machine")
    $envChanged = $true
}

# 清理用户PATH
$userPath = [Environment]::GetEnvironmentVariable("Path", "User")
if ($userPath) {
    $newUserPathEntries = $userPath -split ';' | Where-Object { $_ -notlike '*mysql*' }
    $newUserPath = $newUserPathEntries -join ';'
    if ($userPath -ne $newUserPath) {
        Write-Host "  更新用户PATH环境变量" -ForegroundColor Gray
        [Environment]::SetEnvironmentVariable("Path", $newUserPath, "User")
        $envChanged = $true
    }
}

if (-not $envChanged) {
    Write-Host "  环境变量中未找到MySQL相关路径." -ForegroundColor Green
}

# 9. 删除MySQL相关的临时文件
Write-Host "`n[9/11] 正在删除临时文件..." -ForegroundColor Yellow
$tempPath = Join-Path $env:TEMP "MySQL*"
if (Test-Path $tempPath) {
    Write-Host "  删除临时文件/文件夹: $tempPath" -ForegroundColor Gray
    Remove-Item -Path $tempPath -Recurse -Force -ErrorAction SilentlyContinue
} else {
    Write-Host "  未找到MySQL相关临时文件." -ForegroundColor Green
}

# 10. 检查并终止残留的MySQL进程
Write-Host "`n[10/11] 正在检查并终止残留的MySQL进程..." -ForegroundColor Yellow
$mysqlProcesses = Get-Process | Where-Object {$_.ProcessName -like "mysql*" -or $_.Path -like "*mysql*"} -ErrorAction SilentlyContinue
if ($mysqlProcesses) {
    Write-Host "发现正在运行的MySQL进程，正在终止..." -ForegroundColor Yellow
    $mysqlProcesses | ForEach-Object {
        Write-Host "  终止进程: $($_.ProcessName) (PID: $($_.Id))" -ForegroundColor Gray
        Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
    }
} else {
    Write-Host "  未发现正在运行的MySQL进程." -ForegroundColor Green
}

# --- 验证 --- 

Write-Host "`n[11/11] 正在验证MySQL是否已完全卸载..." -ForegroundColor Cyan
$mysqlExists = $false

# 检查服务
if (Get-Service | Where-Object {$_.Name -like "mysql*" -or $_.DisplayName -like "*mysql*"} -ErrorAction SilentlyContinue) {
    Write-Host "  [失败] 仍然存在MySQL服务." -ForegroundColor Red
    $mysqlExists = $true
}

# 检查程序文件 (部分关键路径)
$checkFolders = @(
    Join-Path $env:ProgramFiles "MySQL",
    Join-Path ${env:ProgramFiles(x86)} "MySQL",
    Join-Path $env:ProgramData "MySQL"
)
foreach ($folder in $checkFolders) {
    if (Test-Path $folder) {
        Write-Host "  [失败] 仍然存在MySQL文件夹: $folder" -ForegroundColor Red
        $mysqlExists = $true
    }
}

# 检查注册表 (部分关键路径)
$checkKeys = @(
    "HKLM:\SOFTWARE\MySQL",
    "HKLM:\SOFTWARE\Wow6432Node\MySQL",
    "HKCU:\Software\MySQL"
)
foreach ($key in $checkKeys) {
    if (Test-Path $key) {
        Write-Host "  [失败] 仍然存在MySQL注册表项: $key" -ForegroundColor Red
        $mysqlExists = $true
    }
}

# 检查环境变量 (简单检查)
$currentSystemPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
$currentUserPath = [Environment]::GetEnvironmentVariable("Path", "User")
if (($currentSystemPath -like '*mysql*') -or ($currentUserPath -like '*mysql*')) {
    Write-Host "  [警告] 环境变量中可能仍存在MySQL路径 (可能需要重启生效)." -ForegroundColor Yellow
    # 不将此标记为 $mysqlExists = $true，因为重启可能解决
}

# --- 总结 --- 

if ($mysqlExists) {
    Write-Host "`nMySQL未能完全卸载。请检查上面的红色失败标记。" -ForegroundColor Red
    Write-Host "建议重启计算机后再次运行此脚本，或手动检查并删除残留项。" -ForegroundColor Yellow
} else {
    Write-Host "`nMySQL已成功彻底卸载！" -ForegroundColor Green
}

Write-Host "`n建议重启计算机以确保所有更改（特别是环境变量和服务删除）完全生效。" -ForegroundColor Cyan
$restartChoice = Read-Host "是否立即重启计算机？(Y/N)"
if ($restartChoice -eq 'Y' -or $restartChoice -eq 'y') {
    Write-Host "正在重启计算机..." -ForegroundColor Magenta
    Restart-Computer -Force
} else {
    Write-Host "脚本执行完毕，请手动重启计算机。" -ForegroundColor White
}

# 脚本结束
Read-Host "按 Enter 键退出..."