# -*- coding: utf-8 -*-
# 运行此脚本需要安装以下依赖：
# 1. 系统工具: ffmpeg (可以通过 Chocolatey 安装: choco install ffmpeg)

import os
import argparse
import sys
import subprocess
import tempfile
import json
import hashlib # 添加 hashlib 用于生成缓存文件名
import re # 导入 re 用于处理非字母字符
from moviepy.editor import VideoFileClip, concatenate_videoclips # 添加 moviepy 导入

# 全局缓存字典，存储已转换的片头/片尾文件路径
# 键: 属性元组 (codec, width, height, fps_str)
# 值: 缓存文件路径
intro_outro_cache = {}

def get_video_properties(file_path):
    """使用 ffprobe 获取视频属性 (编码, 分辨率, 帧率)"""
    try:
        ffprobe_cmd = [
            'ffprobe',
            '-v', 'error',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=codec_name,width,height,avg_frame_rate',
            '-of', 'json',
            file_path
        ]
        result = subprocess.run(ffprobe_cmd, capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore')
        properties = json.loads(result.stdout)['streams'][0]
        # 解析帧率 (可能是分数形式)
        frame_rate_str = properties.get('avg_frame_rate', '0/1')
        if '/' in frame_rate_str:
            num, den = map(int, frame_rate_str.split('/'))
            frame_rate = float(num) / den if den != 0 else 0
        else:
            frame_rate = float(frame_rate_str)

        return {
            'codec': properties.get('codec_name'),
            'width': properties.get('width'),
            'height': properties.get('height'),
            'fps': frame_rate,
            'fps_str': frame_rate_str # 保留原始帧率字符串用于缓存键
        }
    except Exception as e:
        print(f"    警告：无法获取视频属性 '{os.path.basename(file_path)}': {e}")
        return None

def reencode_video(input_path, output_path, target_props):
    """使用 ffmpeg 重新编码视频以匹配目标属性"""
    try:
        # 确保目标属性存在且有效
        if not all(k in target_props and target_props[k] is not None for k in ['codec', 'width', 'height', 'fps']):
            print(f"    错误：重新编码的目标属性不完整或无效: {target_props}")
            return False

        ffmpeg_cmd = [
            'ffmpeg',
            '-i', input_path,
            '-c:v', target_props['codec'],
            '-vf', f"scale={target_props['width']}:{target_props['height']}",
            '-r', str(target_props['fps']), # 使用浮点数帧率
            '-c:a', 'aac', # 假设音频编码为 AAC
            '-y',
            output_path
        ]
        print(f"    执行重新编码命令: {' '.join(ffmpeg_cmd)}")
        result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, check=False, encoding='utf-8', errors='ignore') # check=False

        if result.returncode == 0:
            print(f"    重新编码成功: '{os.path.basename(output_path)}'")
            return True
        else:
            print(f"    重新编码失败 '{os.path.basename(input_path)}':")
            print(f"      返回码: {result.returncode}")
            print(f"      标准错误: {result.stderr[:500]}...") # 打印部分错误
            # 如果失败，尝试删除不完整的输出文件
            if os.path.exists(output_path):
                try: os.remove(output_path)
                except OSError: pass
            return False

    except Exception as e:
        print(f"    重新编码时发生 Python 错误: {e}")
        # 如果发生异常，也尝试删除可能的输出文件
        if os.path.exists(output_path):
            try: os.remove(output_path)
            except OSError: pass
        return False

def convert_filename_to_numeric(filename_base):
    """将文件名中的字母转换为数字，用下划线分隔，保留非字母字符。"""
    numeric_parts = []
    for char in filename_base:
        if 'a' <= char.lower() <= 'z':
            numeric_parts.append(str(ord(char.lower()) - ord('a') + 1))
        else:
            # 保留非字母字符，如果需要替换或删除，修改这里
            numeric_parts.append(char)
    return '_'.join(numeric_parts)

def add_intro_outro_smart(target_dir):
    """智能添加片头片尾：优先快速拼接，失败则使用 moviepy 拼接，并缓存重编码的片头/片尾。"""
    global intro_outro_cache # 引用全局缓存
    script_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    original_intro_outro_file = os.path.join(script_dir, 'powerfull.mp4')
    output_subdir = os.path.join(target_dir, 'processed_videos_smart')
    cache_subdir = os.path.join(output_subdir, '_intro_outro_cache') # 缓存目录放在输出目录下

    if not os.path.isfile(original_intro_outro_file):
        print(f"错误：在脚本目录 '{script_dir}' 中未找到 'powerfull.mp4' 文件。")
        return

    if not os.path.exists(output_subdir):
        os.makedirs(output_subdir)
        print(f"创建输出子目录：'{output_subdir}'")
    if not os.path.exists(cache_subdir):
        os.makedirs(cache_subdir)
        print(f"创建缓存子目录：'{cache_subdir}'")

    video_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.wmv')
    found_videos = False

    print(f"开始智能处理目录 '{target_dir}' 中的视频文件...")

    for filename in os.listdir(target_dir):
        if filename.lower().endswith(video_extensions) and filename != 'powerfull.mp4':
            found_videos = True
            input_path = os.path.join(target_dir, filename)
            # 修改输出文件名生成逻辑
            base_name, ext = os.path.splitext(filename)
            numeric_filename_base = convert_filename_to_numeric(base_name)
            output_filename = f"{numeric_filename_base}{ext}" # 保留原始扩展名
            output_path = os.path.join(output_subdir, output_filename)
            temp_list_file = None
            concat_success = False
            used_intro_outro_file = None # 记录当前使用的片头/片尾文件

            print(f"\n  处理文件：'{filename}' -> 输出为 '{output_filename}'")

            # 1. 获取主视频属性
            main_video_props = get_video_properties(input_path)
            if not main_video_props:
                print("    跳过：无法获取主视频属性，将尝试 moviepy。")
                # 直接跳到策略 3
            else:
                print(f"      获取主视频属性: {main_video_props}")
                # 2. 检查与原始片头/片尾的兼容性
                original_intro_outro_props = get_video_properties(original_intro_outro_file)
                is_compatible = False
                if original_intro_outro_props:
                    # 比较关键属性：编码、宽度、高度、帧率 (允许微小误差)
                    if (original_intro_outro_props['codec'] == main_video_props['codec'] and
                        original_intro_outro_props['width'] == main_video_props['width'] and
                        original_intro_outro_props['height'] == main_video_props['height'] and
                        abs(original_intro_outro_props['fps'] - main_video_props['fps']) < 0.01):
                        is_compatible = True
                        used_intro_outro_file = original_intro_outro_file
                        print("      主视频与原始 'powerfull.mp4' 兼容。")
                    else:
                        print("      主视频与原始 'powerfull.mp4' 不兼容，尝试查找或生成缓存版本。")
                else:
                    print("    警告：无法获取原始 'powerfull.mp4' 的属性。")

                # 3. 如果不兼容，查找或生成缓存版本
                if not is_compatible:
                    # 使用精确的属性（包括原始帧率字符串）生成缓存键
                    cache_key = (
                        main_video_props['codec'],
                        main_video_props['width'],
                        main_video_props['height'],
                        main_video_props['fps_str'] # 使用原始 fps 字符串
                    )
                    # 生成更安全的文件名
                    props_hash = hashlib.md5(str(cache_key).encode()).hexdigest()[:8]
                    cached_filename = f"intro_outro_{props_hash}_{main_video_props['codec']}_{main_video_props['width']}x{main_video_props['height']}_{main_video_props['fps']:.2f}fps.mp4"
                    cached_file_path = os.path.join(cache_subdir, cached_filename)

                    if cached_file_path in intro_outro_cache.values() and os.path.exists(cached_file_path):
                        print(f"      找到缓存版本: '{os.path.basename(cached_file_path)}'")
                        used_intro_outro_file = cached_file_path
                    else:
                        print(f"      未找到缓存版本，开始为属性 {cache_key} 生成缓存...")
                        print(f"      目标缓存文件: '{os.path.basename(cached_file_path)}'")
                        if reencode_video(original_intro_outro_file, cached_file_path, main_video_props):
                            print(f"      成功生成并缓存: '{os.path.basename(cached_file_path)}'")
                            intro_outro_cache[cache_key] = cached_file_path
                            used_intro_outro_file = cached_file_path
                        else:
                            print("      错误：无法生成缓存版本的片头/片尾文件。将尝试 moviepy。")
                            used_intro_outro_file = None # 标记无法使用快速拼接

                # 4. 尝试快速拼接 (如果获得了有效的片头/片尾文件)
                if used_intro_outro_file:
                    print(f"    尝试策略 1: 快速拼接 (ffmpeg concat) 使用 '{os.path.basename(used_intro_outro_file)}'")
                    try:
                        list_content = f"file '{os.path.abspath(used_intro_outro_file)}'\n"
                        list_content += f"file '{os.path.abspath(input_path)}'\n"
                        list_content += f"file '{os.path.abspath(used_intro_outro_file)}'\n"

                        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as tf:
                            tf.write(list_content)
                            temp_list_file = tf.name

                        ffmpeg_cmd = [
                            'ffmpeg',
                            '-f', 'concat',
                            '-safe', '0',
                            '-i', temp_list_file,
                            '-c', 'copy',
                            '-y',
                            output_path
                        ]
                        print(f"      执行 ffmpeg 命令: {' '.join(ffmpeg_cmd)}")
                        result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, check=False, encoding='utf-8', errors='ignore')

                        if result.returncode == 0:
                            print(f"      策略 1 成功: 文件已保存到 '{output_path}'")
                            concat_success = True
                        else:
                            print(f"      策略 1 失败: ffmpeg 返回码 {result.returncode}")
                            print(f"        错误信息: {result.stderr[:500]}...")
                            if os.path.exists(output_path):
                                try: os.remove(output_path)
                                except OSError: pass

                    except Exception as e:
                        print(f"      策略 1 执行时发生 Python 错误: {e}")
                    finally:
                        if temp_list_file and os.path.exists(temp_list_file):
                            try: os.remove(temp_list_file)
                            except OSError: pass
                        temp_list_file = None
                else:
                     # 如果因为获取属性失败或无法生成缓存而跳过了，这里used_intro_outro_file会是None
                     print("    跳过策略 1：无法获取或生成兼容的片头/片尾文件。")

            # --- 策略 2 (原策略 3): 如果策略 1 失败，使用 moviepy ---
            if not concat_success:
                print("    尝试策略 2: 使用 moviepy 拼接 (兼容模式)")
                intro_clip = None
                main_clip = None
                outro_clip = None
                final_clip = None
                try:
                    print("      加载视频剪辑 (moviepy)...")
                    # 始终使用原始的 powerfull.mp4 进行 moviepy 处理
                    intro_clip = VideoFileClip(original_intro_outro_file)
                    main_clip = VideoFileClip(input_path)
                    outro_clip = VideoFileClip(original_intro_outro_file)

                    print("      开始拼接 (moviepy)...")
                    final_clip = concatenate_videoclips([intro_clip, main_clip, outro_clip], method="compose")

                    print("      写入最终文件 (moviepy)...")
                    final_clip.write_videofile(output_path, codec='libx264', audio_codec='aac', logger=None)

                    print(f"      策略 2 (moviepy) 成功: 文件已保存到 '{output_path}'")
                    # 不需要设置 concat_success = True，因为这是最后的策略

                except Exception as e:
                    print(f"      策略 2 (moviepy) 失败: 处理文件 '{filename}' 时 moviepy 出错: {e}")
                    if os.path.exists(output_path):
                        try: os.remove(output_path)
                        except OSError: pass
                    print(f"  处理文件 '{filename}' 失败，所有策略均无效。") # 移到这里
                finally:
                    if intro_clip: intro_clip.close()
                    if main_clip: main_clip.close()
                    if outro_clip: outro_clip.close()
                    if final_clip: final_clip.close()

            # 不再需要单独删除临时重编码文件，因为它们现在是缓存的一部分
            # if temp_intro_outro_encoded and os.path.exists(temp_intro_outro_encoded):
            #     ...

    if not found_videos:
        print(f"在目录 '{target_dir}' 中未找到其他视频文件（不包括 'powerfull.mp4'）。")
    else:
        print("\n处理完成。")

# 主程序入口
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='为指定目录下的所有视频文件智能添加片头和片尾。')
    parser.add_argument('target_directory', type=str, help='包含视频文件的目标目录路径。')

    args = parser.parse_args()

    if not os.path.isdir(args.target_directory):
        print(f"错误：提供的路径 '{args.target_directory}' 不是一个有效的目录。")
        sys.exit(1)

    add_intro_outro_smart(args.target_directory)